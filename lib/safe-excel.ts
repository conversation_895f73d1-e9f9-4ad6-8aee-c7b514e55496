/**
 * 安全的Excel操作封装
 * 用于缓解xlsx包的安全漏洞
 */

import * as XLSX from 'xlsx'

// 限制可解析的文件大小（10MB）
const MAX_FILE_SIZE = 10 * 1024 * 1024

// 限制可处理的行数
const MAX_ROWS = 100000

// 限制可处理的列数
const MAX_COLS = 1000

/**
 * 安全地读取Excel文件
 */
export function safeReadExcel(file: File | ArrayBuffer): Promise<any> {
  return new Promise((resolve, reject) => {
    try {
      // 检查文件大小
      const size = file instanceof File ? file.size : file.byteLength
      if (size > MAX_FILE_SIZE) {
        reject(new Error('文件大小超出限制(10MB)'))
        return
      }

      // 读取工作簿
      const workbook = XLSX.read(file, { 
        type: file instanceof File ? 'binary' : 'array',
        // 安全选项
        cellDates: true,
        cellNF: false,
        cellHTML: false,
        sheetStubs: false,
        bookDeps: false,
        bookFiles: false,
        bookProps: false,
        bookSheets: false,
        bookVBA: false
      })

      const result: any = {}

      // 处理每个工作表
      workbook.SheetNames.forEach(sheetName => {
        const worksheet = workbook.Sheets[sheetName]
        
        // 检查工作表大小
        const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1:A1')
        
        if (range.e.r > MAX_ROWS) {
          throw new Error(`工作表 ${sheetName} 行数超出限制(${MAX_ROWS})`)
        }
        
        if (range.e.c > MAX_COLS) {
          throw new Error(`工作表 ${sheetName} 列数超出限制(${MAX_COLS})`)
        }

        // 转换为JSON数据
        const jsonData = XLSX.utils.sheet_to_json(worksheet, {
          header: 1,
          defval: null,
          blankrows: false,
          raw: false
        })

        result[sheetName] = jsonData
      })

      resolve(result)
    } catch (error: any) {
      reject(new Error(`Excel文件解析失败: ${error.message}`))
    }
  })
}

/**
 * 安全地创建Excel文件
 */
export function safeCreateExcel(data: any, sheetName = 'Sheet1'): ArrayBuffer {
  try {
    // 验证数据
    if (!Array.isArray(data)) {
      throw new Error('数据必须是数组格式')
    }

    if (data.length > MAX_ROWS) {
      throw new Error(`数据行数超出限制(${MAX_ROWS})`)
    }

    // 创建工作簿
    const workbook = XLSX.utils.book_new()
    
    // 创建工作表
    const worksheet = XLSX.utils.aoa_to_sheet(data)
    
    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(workbook, worksheet, sheetName)
    
    // 生成Excel文件
    return XLSX.write(workbook, { 
      type: 'array',
      bookType: 'xlsx',
      compression: true
    })
  } catch (error: any) {
    throw new Error(`Excel文件创建失败: ${error.message}`)
  }
}

/**
 * 验证Excel文件类型
 */
export function validateExcelFile(file: File): boolean {
  const validTypes = [
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-excel',
    'text/csv'
  ]
  
  return validTypes.includes(file.type)
}

/**
 * 清理Excel数据，移除潜在的恶意内容
 */
export function sanitizeExcelData(data: any[][]): any[][] {
  return data.map(row => 
    row.map(cell => {
      if (typeof cell === 'string') {
        // 移除潜在的公式和脚本
        return cell
          .replace(/^[=@+-]/, '') // 移除公式前缀
          .replace(/<script[^>]*>.*?<\/script>/gi, '') // 移除script标签
          .replace(/javascript:/gi, '') // 移除javascript协议
          .replace(/data:text\/html/gi, '') // 移除data URI
          .slice(0, 1000) // 限制字符串长度
      }
      return cell
    })
  )
}