import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/auth'
import type { Session } from 'next-auth'

/**
 * 身份认证中间件，检查用户是否已登录
 */
export function withAuth<T extends any[]>(
  handler: (request: NextRequest, session: Session, ...args: T) => Promise<NextResponse>
) {
  return async (request: NextRequest, ...args: T): Promise<NextResponse> => {
    try {
      const session = await getServerSession(authOptions) as Session | null
      
      if (!session?.user?.id) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      return await handler(request, session, ...args)
    } catch (error: any) {
      console.error('Auth middleware error:', error)
      return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
    }
  }
}

/**
 * 权限检查中间件，检查用户是否有特定权限
 */
export async function withPermission<T extends any[]>(
  permission: string,
  handler: (request: NextRequest, session: Session, ...args: T) => Promise<NextResponse>
) {
  return withAuth(async (request: NextRequest, session: Session, ...args: T) => {
    // 这里可以添加权限检查逻辑
    // 简化版：假设管理员有所有权限
    if (session.user.role === 'admin' || session.user.role === 'super_admin') {
      return await handler(request, session, ...args)
    }

    // 检查用户的具体权限
    try {
      const hasPermission = await checkUserPermission(session.user.id, permission)
      if (!hasPermission) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
      }
      
      return await handler(request, session, ...args)
    } catch (error: any) {
      console.error('Permission check error:', error)
      return NextResponse.json({ error: 'Permission check failed' }, { status: 500 })
    }
  })
}

/**
 * 检查用户权限的辅助函数
 */
async function checkUserPermission(userId: string, permission: string): Promise<boolean> {
  // 这里应该查询数据库检查用户权限
  // 暂时返回true，实际应用中需要实现具体的权限检查逻辑
  return true
}

/**
 * 创建标准化的API响应
 */
export function createApiResponse(data: any, status = 200) {
  return NextResponse.json(data, { status })
}

/**
 * 创建错误响应
 */
export function createErrorResponse(message: string, status = 400) {
  return NextResponse.json({ error: message }, { status })
}

/**
 * 处理API错误的辅助函数
 */
export function handleApiError(error: any, context?: string) {
  console.error(`API Error${context ? ` in ${context}` : ''}:`, error)
  
  if (error.code === 'P2002') {
    return createErrorResponse('Resource already exists', 409)
  }
  
  if (error.code === 'P2025') {
    return createErrorResponse('Resource not found', 404)
  }
  
  return createErrorResponse('Internal server error', 500)
}