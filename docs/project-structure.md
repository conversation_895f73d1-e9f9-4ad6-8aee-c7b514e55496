# 项目结构说明

## 📁 目录组织原则

### 路由组织 (app/)
```
app/
├── (auth)/                 # 认证相关页面
│   ├── login/             # 登录页面
│   ├── register/          # 注册页面
│   ├── reset-password/    # 密码重置
│   └── forgot-password/   # 忘记密码
├── (main)/                # 主要业务功能
│   ├── dashboard/         # 仪表板
│   ├── products/          # 产品管理
│   ├── sales/             # 销售管理
│   ├── inventory/         # 库存管理
│   ├── production/        # 生产管理
│   ├── employees/         # 员工管理
│   ├── customers/         # 客户管理
│   ├── finance/           # 财务管理
│   ├── payroll/           # 工资管理
│   ├── purchase/          # 采购管理
│   ├── reports/           # 报表系统
│   ├── settings/          # 系统设置
│   ├── workflows/         # 工作流程
│   ├── coffee-shop/       # 咖啡店管理
│   ├── workshops/         # 工坊管理
│   ├── schedule/          # 排班管理
│   ├── daily-log/         # 日常记录
│   ├── messages/          # 消息系统
│   ├── notifications/     # 通知中心
│   ├── artworks/          # 艺术品管理
│   ├── channels/          # 渠道管理
│   ├── todos/             # 待办事项
│   ├── help/              # 帮助中心
│   ├── profile/           # 个人资料
│   └── diagnostics/       # 诊断工具
│       ├── database-diagnostics/
│       ├── frontend-diagnostics/
│       ├── network-diagnostics/
│       ├── performance-diagnostics/
│       ├── security-diagnostics/
│       └── system-diagnostics/
├── (mobile)/              # 移动端页面
│   └── m/                 # 移动端路由
├── (fullscreen)/          # 全屏页面
├── api/                   # API 路由
├── layout.tsx             # 根布局
├── page.tsx               # 首页
└── globals.css            # 全局样式
```

### 组件组织 (components/)
```
components/
├── ui/                    # 基础UI组件
│   ├── button.tsx
│   ├── input.tsx
│   ├── table.tsx
│   ├── enhanced-table.tsx # 增强表格组件
│   └── ...
├── forms/                 # 表单组件
├── charts/                # 图表组件
├── layout/                # 布局组件
├── auth/                  # 认证组件
├── mobile/                # 移动端组件
├── product/               # 产品相关组件
├── sales/                 # 销售相关组件
├── inventory/             # 库存相关组件
├── customers/             # 客户相关组件
├── employees/             # 员工相关组件
├── finance/               # 财务相关组件
├── reports/               # 报表相关组件
├── schedule/              # 排班相关组件
├── cost-accounting/       # 成本核算组件
├── personalization/       # 个性化组件
└── providers/             # Context提供者
```

### 工具库组织 (lib/)
```
lib/
├── actions/               # Server Actions
│   ├── product-actions.ts
│   ├── sales-actions.ts
│   ├── employee-actions.ts
│   └── ...
├── utils/                 # 工具函数
├── validations/           # 数据验证
├── hooks/                 # 自定义Hooks
├── api-client.ts          # API客户端
├── database.ts            # 数据库配置
├── auth.ts                # 认证配置
└── constants.ts           # 常量定义
```

### 自定义Hooks (hooks/)
```
hooks/
├── use-loading.ts         # Loading状态管理
├── use-form-state.ts      # 表单状态管理
├── use-toast.ts           # Toast通知
└── ...
```

## 🎯 组织原则

### 1. 功能模块化
- 按业务功能组织目录结构
- 相关功能放在同一模块下
- 避免功能分散和重复

### 2. 层次清晰
- 路由层：app/ 目录按页面组织
- 组件层：components/ 目录按功能组织
- 逻辑层：lib/ 目录按职责组织
- 工具层：hooks/ 目录按用途组织

### 3. 命名规范
- 目录名使用kebab-case
- 组件文件使用PascalCase
- 工具文件使用camelCase
- 常量使用UPPER_CASE

### 4. 依赖关系
- 上层可以依赖下层
- 同层之间避免循环依赖
- 工具层保持独立性

## 📋 已清理的内容

### 移除的目录
- `app/feedback-demo/` - 空的演示目录
- `app/form-demo/` - 空的演示目录
- `app/performance-demo/` - 空的演示目录
- `app/theme-demo/` - 空的演示目录
- `app/visualization-demo/` - 空的演示目录
- `app/test/` - 空的测试目录
- `app/settings/` - 空的设置目录（已合并到main/settings）
- `app/daily-log/` - 空的日志目录（已合并到main/daily-log）

### 重组的目录
- 诊断工具统一到 `app/(main)/diagnostics/`
- 测试和演示目录移至 `archived-backups/`

## 🔄 后续优化建议

### 1. 组件重构
- 将大型组件拆分为更小的子组件
- 提取可重用的业务逻辑组件
- 统一组件的props接口

### 2. 类型定义整理
- 创建统一的类型定义文件
- 按模块组织类型定义
- 提取公共类型到共享文件

### 3. 配置文件统一
- 整理分散的配置文件
- 创建环境特定的配置
- 统一配置文件的格式

### 4. 文档完善
- 为每个模块添加README
- 创建组件使用文档
- 添加API文档
